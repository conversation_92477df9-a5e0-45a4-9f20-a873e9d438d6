(function(){
  'use strict';

  const i18n = {
    ar: {
      brand: 'صيانة بلدي',
      lang_toggle: 'English',
      admin_title: 'لوحة الإدارة',
      btn_export_pdf: 'تصدير PDF',
      btn_clear: 'مسح الكل (محلي)',
      th_type: 'النوع', th_severity: 'الخطورة', th_desc: 'الوصف', th_location: 'الموقع', th_status: 'الحالة', th_actions: 'إجراءات',
      status_new: 'جديد', status_in_progress: 'قيد المعالجة', status_fixed: 'تم الإصلاح', status_rejected: 'مرفوض',
      confirm_clear: 'سيتم مسح جميع البلاغات المحلية. هل أنت متأكد؟'
    },
    en: {
      brand: '<PERSON>yana Baladi',
      lang_toggle: 'العربية',
      admin_title: 'Admin Dashboard',
      btn_export_pdf: 'Export PDF',
      btn_clear: 'Clear All (Local)',
      th_type: 'Type', th_severity: 'Severity', th_desc: 'Description', th_location: 'Location', th_status: 'Status', th_actions: 'Actions',
      status_new: 'New', status_in_progress: 'In progress', status_fixed: 'Fixed', status_rejected: 'Rejected',
      confirm_clear: 'This will delete all local reports. Are you sure?'
    }
  };

  let lang = localStorage.getItem('lang') || 'ar';
  function applyI18n(){
    const dict = i18n[lang];
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.querySelector('#bootstrap-rtl').disabled = lang !== 'ar';
    document.querySelector('#bootstrap-ltr').disabled = lang === 'ar';
    document.querySelectorAll('[data-i18n]').forEach(el => el.textContent = dict[el.getAttribute('data-i18n')] || el.textContent);
  }

  const KEYS = { REPORTS: 'sb_reports_v1' };
  function loadReports(){ try { return JSON.parse(localStorage.getItem(KEYS.REPORTS)) || []; } catch { return []; } }
  function saveReports(list){ localStorage.setItem(KEYS.REPORTS, JSON.stringify(list)); }

  function renderTable(){
    const tbody = document.getElementById('reportsTbody');
    const reports = loadReports();
    tbody.innerHTML = '';
    reports.forEach((r, idx) => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${idx+1}</td>
        <td>${r.category}</td>
        <td>${r.severity}</td>
        <td>${(r.description||'').slice(0,80)}</td>
        <td>${r.lat?.toFixed?.(5) || '-'}, ${r.lng?.toFixed?.(5) || '-'}</td>
        <td>
          <select class="form-select form-select-sm status-select" data-id="${r.id}">
            <option value="new">${t('status_new')}</option>
            <option value="in_progress">${t('status_in_progress')}</option>
            <option value="fixed">${t('status_fixed')}</option>
            <option value="rejected">${t('status_rejected')}</option>
          </select>
        </td>
        <td>
          <button class="btn btn-sm btn-outline-primary" data-action="center" data-lat="${r.lat}" data-lng="${r.lng}"><i class="bi bi-geo"></i></button>
          <button class="btn btn-sm btn-outline-danger" data-action="delete" data-id="${r.id}"><i class="bi bi-trash"></i></button>
        </td>
      `;
      tbody.appendChild(tr);
      tr.querySelector('.status-select').value = r.status || 'new';
    });
  }

  function t(key){ return (i18n[lang]||{})[key] || key; }

  function onChangeStatus(e){
    if (!e.target.classList.contains('status-select')) return;
    const id = e.target.getAttribute('data-id');
    const reports = loadReports();
    const idx = reports.findIndex(r => r.id === id);
    if (idx !== -1) {
      reports[idx].status = e.target.value;
      saveReports(reports);
    }
  }

  function onTableClick(e){
    const btn = e.target.closest('button');
    if (!btn) return;
    const action = btn.getAttribute('data-action');
    if (action === 'delete') {
      const id = btn.getAttribute('data-id');
      const reports = loadReports().filter(r => r.id !== id);
      saveReports(reports);
      renderTable();
    }
  }

  async function exportPdf(){
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    const reports = loadReports();
    doc.setFontSize(14);
    doc.text(t('admin_title'), 14, 18);
    doc.setFontSize(10);

    let y = 26;
    reports.forEach((r, i) => {
      const line = `${i+1}. ${r.category} | ${r.severity} | ${(r.description||'').slice(0,60)} | ${r.status || 'new'}`;
      doc.text(line, 14, y);
      y += 6; if (y > 280) { doc.addPage(); y = 20; }
    });

    doc.save('reports.pdf');
  }

  function clearAll(){
    if (!confirm(t('confirm_clear'))) return;
    localStorage.removeItem('sb_reports_v1');
    renderTable();
  }

  function toggleLang(){
    lang = lang === 'ar' ? 'en' : 'ar';
    localStorage.setItem('lang', lang);
    applyI18n();
    renderTable();
  }

  document.addEventListener('DOMContentLoaded', () => {
    applyI18n();
    renderTable();

    document.getElementById('reportsTbody').addEventListener('change', onChangeStatus);
    document.getElementById('reportsTbody').addEventListener('click', onTableClick);
    document.getElementById('btnExportPdf').addEventListener('click', exportPdf);
    document.getElementById('btnClearAll').addEventListener('click', clearAll);
    document.getElementById('btnLangToggle').addEventListener('click', toggleLang);
  });
})();

