/* Basic styling */
body { font-family: system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji"; }
.map-container { height: 520px; min-height: 360px; }
#offlineBanner { transition: opacity .3s ease; }

/* Leaflet container should fill parent */
.leaflet-container { height: 100%; width: 100%; }

/* Severity color helpers */
.marker-low { filter: hue-rotate(80deg) saturate(1.2); }
.marker-medium { filter: hue-rotate(10deg) saturate(1.1); }
.marker-high { filter: hue-rotate(-50deg) saturate(1.4) brightness(1.05); }

/* RTL / LTR toggling utility */
:root[dir="rtl"] .ltr-only { display: none !important; }
:root[dir="ltr"] .rtl-only { display: none !important; }

