<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>صيانة بلدي</title>

    <!-- Bootstrap 5 RTL & LTR -->
    <link id="bootstrap-rtl" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet" integrity="sha384-3WZ1dBA8rW8WbGzQ4F6JmW6vJ8P3xgJxdrJfM9B4bQ9QdK1RzKxj3u2m2oQnAE3X" crossorigin="anonymous">
    <link id="bootstrap-ltr" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" disabled>

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" />

    <!-- Leaflet (Map) -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

    <!-- Custom styles -->
    <link rel="stylesheet" href="assets/styles.css" />
  </head>
  <body class="bg-light">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg bg-white border-bottom sticky-top">
      <div class="container-fluid">
        <a class="navbar-brand fw-bold" href="#" data-i18n="brand">صيانة بلدي</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarContent">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="#" data-i18n="nav_report">إرسال بلاغ</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#map" data-i18n="nav_map">الخريطة</a>
            </li>
          </ul>
          <div class="d-flex gap-2">
            <span id="onlineStatus" class="badge text-bg-success" data-i18n="status_online">متصل</span>
            <button id="btnLangToggle" class="btn btn-outline-primary btn-sm" type="button">
              <i class="bi bi-translate"></i> <span data-i18n="lang_toggle">English</span>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Offline banner -->
    <div id="offlineBanner" class="alert alert-warning text-center border-0 rounded-0 py-2 d-none" role="alert" data-i18n="offline_banner">
      أنت غير متصل الآن. سيتم حفظ البلاغ كمسودة ورفعه تلقائيًا عند الاتصال.
    </div>

    <main class="container my-4">
      <div class="row g-4">
        <!-- Report form -->
        <div class="col-12 col-lg-5">
          <div class="card shadow-sm">
            <div class="card-header bg-white">
              <h5 class="card-title mb-0" data-i18n="report_title">إرسال بلاغ جديد</h5>
            </div>
            <div class="card-body">
              <form id="reportForm">
                <div class="mb-3">
                  <label class="form-label" for="category" data-i18n="field_category">نوع المشكلة</label>
                  <select id="category" class="form-select" required>
                    <option value="pothole" selected data-i18n="cat_pothole">حفرة</option>
                    <option value="speed_bump" data-i18n="cat_speed_bump">مطب</option>
                    <option value="water" data-i18n="cat_water">تجمع مياه</option>
                    <option value="sidewalk" data-i18n="cat_sidewalk">انهيار رصيف</option>
                    <option value="signal" data-i18n="cat_signal">إشارة معطلة</option>
                    <option value="blocked_road" data-i18n="cat_blocked_road">طريق مسدود</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label" for="severity" data-i18n="field_severity">مستوى الخطورة</label>
                  <select id="severity" class="form-select" required>
                    <option value="low" data-i18n="sev_low">منخفض</option>
                    <option value="medium" selected data-i18n="sev_medium">متوسط</option>
                    <option value="high" data-i18n="sev_high">عالي</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label" for="description" data-i18n="field_description">وصف مختصر</label>
                  <textarea id="description" class="form-control" rows="3" maxlength="400" placeholder="..." data-i18n-placeholder="ph_description"></textarea>
                  <div class="form-text" data-i18n="desc_help">بحد أقصى 400 حرف.</div>
                </div>

                <div class="mb-3">
                  <label class="form-label" for="photo" data-i18n="field_photo">صورة المشكلة</label>
                  <input id="photo" class="form-control" type="file" accept="image/*" capture="environment" />
                  <div class="mt-2 text-center">
                    <img id="photoPreview" class="img-fluid rounded d-none" alt="preview" />
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label" data-i18n="field_location">الموقع</label>
                  <div class="d-flex flex-wrap gap-2 mb-2">
                    <button id="btnGeolocate" type="button" class="btn btn-outline-secondary btn-sm">
                      <i class="bi bi-geo-alt"></i> <span data-i18n="btn_geolocate">تحديد تلقائي</span>
                    </button>
                    <button id="btnPickOnMap" type="button" class="btn btn-outline-secondary btn-sm">
                      <i class="bi bi-pin-map"></i> <span data-i18n="btn_pick_on_map">اختيار من الخريطة</span>
                    </button>
                  </div>
                  <div class="row g-2">
                    <div class="col-6">
                      <input id="lat" class="form-control" type="text" placeholder="Lat" readonly />
                    </div>
                    <div class="col-6">
                      <input id="lng" class="form-control" type="text" placeholder="Lng" readonly />
                    </div>
                  </div>
                </div>

                <div class="d-flex gap-2">
                  <button id="btnSubmit" type="submit" class="btn btn-primary flex-fill">
                    <i class="bi bi-send"></i> <span data-i18n="btn_submit">إرسال البلاغ</span>
                  </button>
                  <button id="btnSaveDraft" type="button" class="btn btn-outline-secondary">
                    <i class="bi bi-save"></i> <span data-i18n="btn_save_draft">حفظ كمسودة</span>
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Filters -->
          <div class="card shadow-sm mt-4">
            <div class="card-header bg-white">
              <h6 class="mb-0" data-i18n="filters_title">تصفية البلاغات</h6>
            </div>
            <div class="card-body">
              <div class="row g-2">
                <div class="col-6">
                  <label class="form-label" data-i18n="filters_type">النوع</label>
                  <select id="filterType" class="form-select">
                    <option value="all" selected data-i18n="filters_all">الكل</option>
                    <option value="pothole" data-i18n="cat_pothole">حفرة</option>
                    <option value="speed_bump" data-i18n="cat_speed_bump">مطب</option>
                    <option value="water" data-i18n="cat_water">تجمع مياه</option>
                    <option value="sidewalk" data-i18n="cat_sidewalk">انهيار رصيف</option>
                    <option value="signal" data-i18n="cat_signal">إشارة معطلة</option>
                    <option value="blocked_road" data-i18n="cat_blocked_road">طريق مسدود</option>
                  </select>
                </div>
                <div class="col-6">
                  <label class="form-label" data-i18n="filters_severity">الخطورة</label>
                  <select id="filterSeverity" class="form-select">
                    <option value="all" selected data-i18n="filters_all">الكل</option>
                    <option value="low" data-i18n="sev_low">منخفض</option>
                    <option value="medium" data-i18n="sev_medium">متوسط</option>
                    <option value="high" data-i18n="sev_high">عالي</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Map -->
        <div class="col-12 col-lg-7">
          <div class="card shadow-sm h-100">
            <div class="card-header bg-white d-flex align-items-center justify-content-between">
              <h6 class="mb-0" data-i18n="map_title">الخريطة التفاعلية</h6>
              <small class="text-muted" data-i18n="map_hint">تكبير/تصغير واسحب لاختيار الموقع</small>
            </div>
            <div class="card-body p-0">
              <div id="map" class="map-container"></div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <footer class="py-4 border-top bg-white">
      <div class="container d-flex flex-wrap justify-content-between align-items-center gap-3">
        <span class="text-muted" data-i18n="footer_text">© 2025 صيانة بلدي</span>
        <div class="small text-muted" data-i18n="footer_hint">هذا نموذج أولي. لا يتم إرسال البيانات فعليًا بعد.</div>
      </div>
    </footer>

    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <!-- App JS -->
    <script src="assets/app.js"></script>
  </body>
</html>

