/* صيانة بلدي - تطبيق واجهة أمامية بدون خادم */
(function () {
  'use strict';

  // i18n strings
  const i18n = {
    ar: {
      brand: 'صيانة بلدي',
      nav_report: 'إرسال بلاغ',
      nav_map: 'الخريطة',
      status_online: 'متصل',
      status_offline: 'غير متصل',
      offline_banner: 'أنت غير متصل الآن. سيتم حفظ البلاغ كمسودة ورفعه تلقائيًا عند الاتصال.',
      report_title: 'إرسال بلاغ جديد',
      field_category: 'نوع المشكلة',
      field_severity: 'مستوى الخطورة',
      field_description: 'وصف مختصر',
      desc_help: 'بحد أقصى 400 حرف.',
      field_photo: 'صورة المشكلة',
      field_location: 'الموقع',
      btn_geolocate: 'تحديد تلقائي',
      btn_pick_on_map: 'اختيار من الخريطة',
      btn_submit: 'إرسال البلاغ',
      btn_save_draft: 'حفظ كمسودة',
      filters_title: 'تصفية البلاغات',
      filters_type: 'النوع',
      filters_severity: 'الخطورة',
      filters_all: 'الكل',
      map_title: 'الخريطة التفاعلية',
      map_hint: 'تكبير/تصغير واسحب لاختيار الموقع',
      footer_text: '© 2025 صيانة بلدي',
      footer_hint: 'هذا نموذج أولي. لا يتم إرسال البيانات فعليًا بعد.',
      lang_toggle: 'English',
      sev_low: 'منخفض',
      sev_medium: 'متوسط',
      sev_high: 'عالي',
      cat_pothole: 'حفرة',
      cat_speed_bump: 'مطب',
      cat_water: 'تجمع مياه',
      cat_sidewalk: 'انهيار رصيف',
      cat_signal: 'إشارة معطلة',
      cat_blocked_road: 'طريق مسدود',
    },
    en: {
      brand: 'Siyana Baladi',
      nav_report: 'Submit Report',
      nav_map: 'Map',
      status_online: 'Online',
      status_offline: 'Offline',
      offline_banner: 'You are offline. The report will be saved as a draft and auto-uploaded when connected.',
      report_title: 'New Report',
      field_category: 'Issue Type',
      field_severity: 'Severity',
      field_description: 'Short Description',
      desc_help: 'Max 400 characters.',
      field_photo: 'Photo',
      field_location: 'Location',
      btn_geolocate: 'Auto Locate',
      btn_pick_on_map: 'Pick on Map',
      btn_submit: 'Submit',
      btn_save_draft: 'Save Draft',
      filters_title: 'Filters',
      filters_type: 'Type',
      filters_severity: 'Severity',
      filters_all: 'All',
      map_title: 'Interactive Map',
      map_hint: 'Zoom and drag to pick a location',
      footer_text: '© 2025 Siyana Baladi',
      footer_hint: 'Prototype - data not actually sent yet.',
      lang_toggle: 'العربية',
      sev_low: 'Low',
      sev_medium: 'Medium',
      sev_high: 'High',
      cat_pothole: 'Pothole',
      cat_speed_bump: 'Speed bump',
      cat_water: 'Water pooling',
      cat_sidewalk: 'Sidewalk collapse',
      cat_signal: 'Signal out',
      cat_blocked_road: 'Blocked road',
    }
  };

  const $ = (sel) => document.querySelector(sel);
  const $$ = (sel) => Array.from(document.querySelectorAll(sel));

  // Language handling (default Arabic)
  let lang = localStorage.getItem('lang') || 'ar';
  function applyI18n() {
    const dict = i18n[lang];
    $$('[data-i18n]').forEach(el => { el.textContent = dict[el.getAttribute('data-i18n')] || el.textContent; });
    $$('[data-i18n-placeholder]').forEach(el => { el.placeholder = dict[el.getAttribute('data-i18n-placeholder')] || el.placeholder; });
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    // Toggle RTL/LTR Bootstrap
    $('#bootstrap-rtl').disabled = lang !== 'ar';
    $('#bootstrap-ltr').disabled = lang === 'ar';
  }

  // Storage keys
  const KEYS = {
    REPORTS: 'sb_reports_v1',
    DRAFTS: 'sb_drafts_v1',
    DAILY_COUNT: 'sb_daily_count_v1',
    DAILY_DATE: 'sb_daily_date_v1'
  };

  const DAILY_LIMIT = 10; // per device

  function getStored(key, fallback) {
    try { return JSON.parse(localStorage.getItem(key)) ?? fallback; } catch { return fallback; }
  }
  function setStored(key, val) { localStorage.setItem(key, JSON.stringify(val)); }

  // Basic anti-abuse: limit per day
  function canSubmitToday() {
    const today = new Date().toISOString().slice(0,10);
    let date = localStorage.getItem(KEYS.DAILY_DATE);
    let count = parseInt(localStorage.getItem(KEYS.DAILY_COUNT) || '0', 10);
    if (date !== today) {
      localStorage.setItem(KEYS.DAILY_DATE, today);
      localStorage.setItem(KEYS.DAILY_COUNT, '0');
      return true;
    }
    return count < DAILY_LIMIT;
  }
  function incrementDailyCount() {
    const today = new Date().toISOString().slice(0,10);
    localStorage.setItem(KEYS.DAILY_DATE, today);
    let count = parseInt(localStorage.getItem(KEYS.DAILY_COUNT) || '0', 10) + 1;
    localStorage.setItem(KEYS.DAILY_COUNT, String(count));
  }

  // Map init
  let map, markerPick;
  function initMap() {
    map = L.map('map').setView([24.7136, 46.6753], 12); // Default to Riyadh
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; OpenStreetMap'
    }).addTo(map);
  }

  // Reports store
  function loadReports() { return getStored(KEYS.REPORTS, []); }
  function saveReports(reports) { setStored(KEYS.REPORTS, reports); }

  // Create a marker color by severity
  function severityColor(sev) {
    return sev === 'high' ? 'red' : sev === 'medium' ? 'orange' : 'green';
  }

  const markersLayer = L.layerGroup();

  function renderMarkers(filter = {}) {
    markersLayer.clearLayers();
    const reports = loadReports();
    reports.filter(r => {
      let ok = true;
      if (filter.type && filter.type !== 'all') ok = ok && r.category === filter.type;
      if (filter.severity && filter.severity !== 'all') ok = ok && r.severity === filter.severity;
      return ok;
    }).forEach(r => {
      if (!r.lat || !r.lng) return;
      const m = L.circleMarker([r.lat, r.lng], {
        radius: 8,
        color: severityColor(r.severity),
        fillColor: severityColor(r.severity),
        fillOpacity: 0.8,
        weight: 2,
      }).bindPopup(`<strong>${r.category}</strong><br>${r.severity}<br>${r.description || ''}`);
      markersLayer.addLayer(m);
    });
    markersLayer.addTo(map);
  }

  // Geolocation
  function geolocate() {
    if (!navigator.geolocation) return;
    navigator.geolocation.getCurrentPosition((pos) => {
      const { latitude, longitude } = pos.coords;
      $('#lat').value = latitude.toFixed(6);
      $('#lng').value = longitude.toFixed(6);
      map.setView([latitude, longitude], 16);
    });
  }

  // Image preview
  function handlePhotoChange(e) {
    const file = e.target.files?.[0];
    const img = $('#photoPreview');
    if (!file) { img.classList.add('d-none'); img.src = ''; return; }
    const reader = new FileReader();
    reader.onload = () => { img.src = String(reader.result); img.classList.remove('d-none'); };
    reader.readAsDataURL(file);
  }

  // Gather form data
  function collectFormData() {
    const category = $('#category').value;
    const severity = $('#severity').value;
    const description = $('#description').value.trim();
    const lat = parseFloat($('#lat').value);
    const lng = parseFloat($('#lng').value);
    const photoEl = $('#photo');

    return new Promise((resolve) => {
      const file = photoEl.files?.[0];
      if (!file) return resolve({ category, severity, description, lat, lng, photo: null, status: 'new', createdAt: Date.now() });
      const reader = new FileReader();
      reader.onload = () => resolve({ category, severity, description, lat, lng, photo: String(reader.result), status: 'new', createdAt: Date.now() });
      reader.readAsDataURL(file);
    });
  }

  // Drafts
  function loadDrafts() { return getStored(KEYS.DRAFTS, []); }
  function saveDrafts(drafts) { setStored(KEYS.DRAFTS, drafts); }

  async function saveAsDraft() {
    const data = await collectFormData();
    const drafts = loadDrafts();
    drafts.push({ id: crypto.randomUUID(), ...data });
    saveDrafts(drafts);
    alert(lang === 'ar' ? 'تم حفظ المسودة محليًا' : 'Draft saved locally');
  }

  // Submit report (local only for now)
  async function submitReport() {
    if (!canSubmitToday()) {
      alert(lang === 'ar' ? 'تم بلوغ الحد اليومي للبلاغات.' : 'Daily report limit reached.');
      return;
    }
    const data = await collectFormData();
    const reports = loadReports();
    reports.push({ id: crypto.randomUUID(), ...data });
    saveReports(reports);
    incrementDailyCount();
    renderMarkers(currentFilter());
    alert(lang === 'ar' ? 'تم حفظ البلاغ محليًا.' : 'Report saved locally.');
    $('#reportForm').reset();
    $('#photoPreview').classList.add('d-none');
  }

  // Pick on map
  function enablePickOnMap() {
    if (markerPick) { map.removeLayer(markerPick); markerPick = null; }
    const handler = (e) => {
      const { lat, lng } = e.latlng;
      $('#lat').value = lat.toFixed(6);
      $('#lng').value = lng.toFixed(6);
      if (markerPick) { map.removeLayer(markerPick); }
      markerPick = L.marker([lat, lng]).addTo(map);
      map.off('click', handler);
    };
    map.on('click', handler);
  }

  // Filters
  function currentFilter() {
    return { type: $('#filterType').value, severity: $('#filterSeverity').value };
  }

  // Online/offline UI
  function updateOnlineStatus() {
    const online = navigator.onLine;
    $('#onlineStatus').textContent = online ? i18n[lang].status_online : i18n[lang].status_offline;
    $('#onlineStatus').className = `badge ${online ? 'text-bg-success' : 'text-bg-secondary'}`;
    $('#offlineBanner').classList.toggle('d-none', online);
  }

  // Language toggle
  function toggleLang() {
    lang = lang === 'ar' ? 'en' : 'ar';
    localStorage.setItem('lang', lang);
    applyI18n();
  }

  // Init
  document.addEventListener('DOMContentLoaded', () => {
    applyI18n();
    initMap();
    renderMarkers();

    // Events
    $('#btnGeolocate').addEventListener('click', geolocate);
    $('#btnPickOnMap').addEventListener('click', enablePickOnMap);
    $('#photo').addEventListener('change', handlePhotoChange);
    $('#reportForm').addEventListener('submit', (e) => { e.preventDefault(); submitReport(); });
    $('#btnSaveDraft').addEventListener('click', saveAsDraft);
    $('#filterType').addEventListener('change', () => renderMarkers(currentFilter()));
    $('#filterSeverity').addEventListener('change', () => renderMarkers(currentFilter()));
    $('#btnLangToggle').addEventListener('click', toggleLang);

    // Online/Offline
    updateOnlineStatus();
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
  });
})();

